#include "../passthrough_link.h"
#include "../async_bridge.h"
#include "../stream_node.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>

using namespace SPipeline;

// Example data types
struct SampleData {
    int value;
    uint64_t timestamp;
    
    SampleData(int v = 0, uint64_t ts = 0) : value(v), timestamp(ts) {}
};

struct ProcessedData {
    int processed_value;
    uint64_t timestamp;
    
    ProcessedData(int v = 0, uint64_t ts = 0) : processed_value(v), timestamp(ts) {}
};

// Example data source node
class DataSource : public StreamNode<int, SampleData> {
private:
    std::atomic<bool> running_{false};
    int counter_ = 0;
    
public:
    bool process(int&& trigger) override {
        // Generate sample data
        auto now = std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();

        SampleData data(counter_++, now);
        std::cout << "DataSource: Generated sample " << data.value
                  << " at " << data.timestamp << std::endl;
        return sendOutput(std::move(data));
    }
    
    bool tick() override {
        if (running_.load()) {
            int trigger = 1;
            process(std::move(trigger));
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            return true;
        }
        return false;
    }
    
    bool hasPendingWork() const override {
        return running_.load();
    }
    
    void start() { running_ = true; }
    void stop() { running_ = false; }
};

// Example processing node
class DataProcessor : public StreamNode<SampleData, ProcessedData> {
public:
    bool process(SampleData&& input) override {
        // Simple processing: multiply by 2
        ProcessedData result(input.value * 2, input.timestamp);
        std::cout << "DataProcessor: Processed " << input.value
                  << " -> " << result.processed_value << std::endl;
        return sendOutput(std::move(result));
    }
};

// Example sink node
class DataSink : public StreamNode<ProcessedData, int> {
public:
    bool process(ProcessedData&& input) override {
        std::cout << "DataSink: Received processed value "
                  << input.processed_value << " at " << input.timestamp << std::endl;
        return true;  // Continue processing
    }
};

// Multi-threaded tick manager
class TickManager {
private:
    std::vector<std::thread> threads_;
    std::atomic<bool> running_{false};

public:
    void start() {
        running_ = true;
    }

    void addTickable(PipelineComponent* tickable, const std::string& name) {
        threads_.emplace_back([this, tickable, name]() {
            std::cout << "Starting tick thread for " << name << std::endl;

            while (running_.load()) {
                bool hasWork = tickable->tick();

                if (!hasWork && !tickable->hasPendingWork()) {
                    // No work to do, sleep briefly
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
            }

            std::cout << "Stopping tick thread for " << name << std::endl;
        });
    }

    void stop() {
        running_ = false;
        for (auto& thread : threads_) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        threads_.clear();
    }
};

int main() {
    std::cout << "=== Stream Pipeline Basic Example ===" << std::endl;
    
    // Create pipeline components
    DataSource source;
    DataProcessor processor;
    DataSink sink;
    
    // Create links
    PassthroughLink<SampleData> passthroughLink;
    AsyncBridge<ProcessedData> asyncBridge(50);  // Buffer up to 50 items
    
    std::cout << "Created pipeline components and links" << std::endl;
    
    // Connect same-thread components using PassthroughLink
    source.connectOutputLink(&passthroughLink);
    passthroughLink.onOutput([&processor](SampleData&& data) -> bool {
        return processor.process(std::move(data));
    });
    
    // Connect cross-thread components using AsyncBridge
    processor.connectOutputLink(&asyncBridge);
    asyncBridge.onOutput([&sink](ProcessedData&& data) -> bool {
        return sink.process(std::move(data));
    });
    
    std::cout << "Connected pipeline components" << std::endl;
    
    // Setup multi-threaded tick loop
    TickManager tickManager;

    std::cout << "Setup tick manager" << std::endl;

    // Start the pipeline
    source.start();
    tickManager.start();

    // Add components to tick manager (after starting)
    tickManager.addTickable(&source, "DataSource");
    tickManager.addTickable(&passthroughLink, "PassthroughLink");
    tickManager.addTickable(&asyncBridge, "AsyncBridge");
    
    std::cout << "Pipeline started, running for 5 seconds..." << std::endl;
    
    // Run for 5 seconds
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    std::cout << "Initiating shutdown..." << std::endl;
    
    // Proper shutdown sequence
    source.stop();                    // Stop data generation
    std::this_thread::sleep_for(std::chrono::milliseconds(200));  // Allow pending data to flow
    asyncBridge.stop();           // Shutdown async components
    tickManager.stop();               // Stop all tick threads
    
    std::cout << "Pipeline shutdown complete" << std::endl;
    std::cout << "Final AsyncBridge buffer size: " << asyncBridge.size() << std::endl;
    
    return 0;
}
