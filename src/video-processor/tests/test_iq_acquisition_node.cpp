#include <iostream>
#include <cassert>
#include <memory>
#include "../pipeline/iq_acquisition_node.h"
#include "../../iiq-stream/iiq_stream.h"

namespace IQAcquisitionNodeTests {

// Mock IIQStream for testing
class MockIIQStream : public IIQStream {
public:
    MockIIQStream(SampleRateType sampleRate = 20000000, bool active = true) 
        : sampleRate_(sampleRate), active_(active), sourceName_("mock") {}

    bool readSamples(SampleType* dst, size_t sampleCount) override {
        if (!active_) return false;
        
        // Fill with test pattern
        for (size_t i = 0; i < sampleCount; ++i) {
            dst[i] = static_cast<SampleType>(i & 0xFFFFFFFF);
        }
        return true;
    }

    SampleRateType sampleRate() const noexcept override {
        return sampleRate_;
    }

    const std::string& sourceName() const noexcept override {
        return sourceName_;
    }

    bool isActive() const noexcept override {
        return active_;
    }

    void close() noexcept override {
        active_ = false;
    }

    const std::string& lastError() const noexcept override {
        return error_;
    }

    void setActive(bool active) { active_ = active; }

private:
    SampleRateType sampleRate_;
    bool active_;
    std::string sourceName_;
    std::string error_;
};

void test_construction_with_null_stream() {
    std::cout << "Testing IQAcquisitionNode construction with null stream..." << std::endl;
    
    // Should handle null stream gracefully
    auto node = std::make_unique<IQVideoProcessor::Pipeline::IQAcquisitionNode>(nullptr);
    
    std::cout << "✓ IQAcquisitionNode constructed with null stream" << std::endl;
}

void test_construction_with_valid_stream() {
    std::cout << "Testing IQAcquisitionNode construction with valid stream..." << std::endl;
    
    auto mockStream = std::make_unique<MockIIQStream>();
    auto node = std::make_unique<IQVideoProcessor::Pipeline::IQAcquisitionNode>(std::move(mockStream));
    
    std::cout << "✓ IQAcquisitionNode constructed with valid stream" << std::endl;
}

void test_sample_rate_configuration() {
    std::cout << "Testing IQAcquisitionNode sample rate configuration..." << std::endl;
    
    // Test different sample rates
    std::vector<SampleRateType> testRates = {1000000, 10000000, 20000000, 40000000};
    
    for (auto rate : testRates) {
        auto mockStream = std::make_unique<MockIIQStream>(rate);
        auto node = std::make_unique<IQVideoProcessor::Pipeline::IQAcquisitionNode>(std::move(mockStream));
        
        // Node should be constructed successfully with any valid sample rate
        std::cout << "✓ Sample rate " << rate << " Hz configured successfully" << std::endl;
    }
}

void test_process_method() {
    std::cout << "Testing IQAcquisitionNode process method..." << std::endl;
    
    auto mockStream = std::make_unique<MockIIQStream>();
    auto node = std::make_unique<IQVideoProcessor::Pipeline::IQAcquisitionNode>(std::move(mockStream));
    
    // Process method should return false (this node doesn't process input directly)
    bool result = node->process(true);
    assert(result == false);
    
    std::cout << "✓ Process method returns expected value" << std::endl;
}

void test_has_pending_work() {
    std::cout << "Testing IQAcquisitionNode hasPendingWork method..." << std::endl;
    
    auto mockStream = std::make_unique<MockIIQStream>();
    auto node = std::make_unique<IQVideoProcessor::Pipeline::IQAcquisitionNode>(std::move(mockStream));
    
    // Should return false (no pending work in current implementation)
    bool result = node->hasPendingWork();
    assert(result == false);
    
    std::cout << "✓ HasPendingWork method returns expected value" << std::endl;
}

void test_tick_with_active_stream() {
    std::cout << "Testing IQAcquisitionNode tick with active stream..." << std::endl;
    
    auto mockStream = std::make_unique<MockIIQStream>();
    auto node = std::make_unique<IQVideoProcessor::Pipeline::IQAcquisitionNode>(std::move(mockStream));
    
    // Tick should work with active stream
    bool result = node->tick();
    // Result depends on whether output callback is set, but should not crash
    
    std::cout << "✓ Tick method executed without errors" << std::endl;
}

void test_tick_with_inactive_stream() {
    std::cout << "Testing IQAcquisitionNode tick with inactive stream..." << std::endl;
    
    auto mockStream = std::make_unique<MockIIQStream>();
    mockStream->setActive(false);
    auto node = std::make_unique<IQVideoProcessor::Pipeline::IQAcquisitionNode>(std::move(mockStream));
    
    // Tick should handle inactive stream gracefully
    bool result = node->tick();
    assert(result == false);
    
    std::cout << "✓ Tick method handles inactive stream correctly" << std::endl;
}

void test_shutdown() {
    std::cout << "Testing IQAcquisitionNode shutdown..." << std::endl;
    
    auto mockStream = std::make_unique<MockIIQStream>();
    auto node = std::make_unique<IQVideoProcessor::Pipeline::IQAcquisitionNode>(std::move(mockStream));
    
    // Shutdown should work without errors
    node->stop();
    
    // After shutdown, tick should return false
    bool result = node->tick();
    assert(result == false);
    
    std::cout << "✓ Shutdown method works correctly" << std::endl;
}

void test_destructor() {
    std::cout << "Testing IQAcquisitionNode destructor..." << std::endl;
    
    {
        auto mockStream = std::make_unique<MockIIQStream>();
        auto node = std::make_unique<IQVideoProcessor::Pipeline::IQAcquisitionNode>(std::move(mockStream));
        // Destructor should be called automatically
    }
    
    std::cout << "✓ Destructor handled correctly" << std::endl;
}

} // namespace IQAcquisitionNodeTests

// Main test runner function
int run_iq_acquisition_node_tests() {
    std::cout << "\n🧪 Running IQAcquisitionNode Tests" << std::endl;
    std::cout << "==================================" << std::endl;

    try {
        IQAcquisitionNodeTests::test_construction_with_null_stream();
        IQAcquisitionNodeTests::test_construction_with_valid_stream();
        IQAcquisitionNodeTests::test_sample_rate_configuration();
        IQAcquisitionNodeTests::test_process_method();
        IQAcquisitionNodeTests::test_has_pending_work();
        IQAcquisitionNodeTests::test_tick_with_active_stream();
        IQAcquisitionNodeTests::test_tick_with_inactive_stream();
        IQAcquisitionNodeTests::test_shutdown();
        IQAcquisitionNodeTests::test_destructor();

        std::cout << "\n🎉 All IQAcquisitionNode tests PASSED!" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "\n❌ IQAcquisitionNode test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "\n❌ IQAcquisitionNode test failed with unknown exception" << std::endl;
        return 1;
    }
}
